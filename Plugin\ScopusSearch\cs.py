import requests
import json

# 1. 替换成你自己的API Key
MY_API_KEY = "1bf5408b6ed858349b2816cdb85d0b5e"

# 2. 构建API请求URL
# Scopus Search API的端点
base_url = "https://api.elsevier.com/content/search/scopus"

# 3. 设置查询参数
query = "TITLE-ABS-KEY(machine learning)" # 在标题、摘要、关键词中搜索 "machine learning"

params = {
    "query": query,
    "apiKey": MY_API_KEY,
    "count": 5  # 只获取前5条结果作为示例
}

# 4. 发送GET请求
try:
    response = requests.get(base_url, params=params)
    response.raise_for_status()  # 如果请求失败(如401, 404), 会抛出异常

    # 5. 解析并打印结果
    data = response.json()

    # 打印返回的文献条目
    entries = data.get('search-results', {}).get('entry', [])
    print(f"找到了 {len(entries)} 篇文献：\n")

    for i, entry in enumerate(entries):
        title = entry.get('dc:title', 'N/A')
        creator = entry.get('dc:creator', 'N/A')
        publication = entry.get('prism:publicationName', 'N/A')
        print(f"{i+1}. 标题: {title}")
        print(f"   作者: {creator}")
        print(f"   期刊: {publication}\n")

except requests.exceptions.RequestException as e:
    print(f"请求出错: {e}")
except json.JSONDecodeError:
    print("解析返回的JSON数据失败。")
    print("原始返回内容:", response.text)

