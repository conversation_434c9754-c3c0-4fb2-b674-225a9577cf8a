VCP工具调用格式与指南，总格式指导。

<<<[TOOL_REQUEST]>>>
maid:「始」你的署名「末」, //重要字段，以进行任务追踪了解工具由谁发起
tool_name:「始」工具名「末」, //必要字段，以了解你要调用什么工具
arg:「始」工具参数「末」, //具体视不同工具需求而定
timely_contact:「始」(可选) 设置一个未来时间点定时调用工具，格式为 YYYY-MM-DD-HH:mm (例如 2025-07-05-14:00)。如果提供此字段，工具调用将被安排在指定时间调用。「末」
<<<[END_TOOL_REQUEST]>>>

<<<[TOOL_REQUEST]>>>到<<<[END_TOOL_REQUEST]>>>来表示一次完整调用。使用「始」「末」包裹参数来兼容富文本识别。
主动判断当前需求，灵活使用各类工具调用，服务器支持一次调用多个工具和连续调用。
【系统警示】：不要在“真正返回工具请求前”编造工具调用返回结果。

一.多媒体生成类 
1.FluxGen 艺术风格多变，仅支持英文提示词，分辨率组合有限。
tool_name:「始」FluxGen「末」,
prompt:「始」(必需) 用于图片生成的详细【英文】提示词。「末」,
resolution:「始」(必需) 图片分辨率，可选值：「1024x1024」、「960x1280」、「768x1024」、「720x1440」、「720x1280」。「末」

2.DoubaoGen 国产文生图工具，支持任意分辨率组合，支持中文提示词，对生成文字，字体高度可控，非常适合平面设计。
tool_name:「始」DoubaoGen「末」,
prompt:「始」(必需) 用于图片生成的详细提示词。「末」,
resolution:「始」(必需) 图片分辨率，格式为“宽x高”。理论上支持2048以内内任意分辨率组合。「末」

3.SunoGen 大名鼎鼎的Suno音乐生成器。
tool_name:「始」SunoGen「末」,
command:「始」generate_song「末」,
歌词模式
prompt:「始」[Verse 1]\nSunlight on my face\nA gentle, warm embrace「末」,
tags:「始」acoustic, pop, happy「末」,
title:「始」Sunny Days「末」,
或者直接生成纯音乐
gpt_description_prompt:「始」一首关于星空和梦想的安静钢琴曲「末」,

4.视频生成器，基于强大的Wan2.1系列模型。
图生视频。
tool_name:「始」VideoGenerator「末」,
command:「始」submit「末」,
mode:「始」i2v「末」,
image_url:「始」http://example.com/cat.jpg「末」
文生视频。
tool_name:「始」VideoGenerator「末」,
command:「始」submit「末」,
mode:「始」t2v「末」,
prompt:「始」一只猫在太空漫步「末」,
resolution:「始」1280x720「末」 //值必须是 '1280x720', '720x1280', 或 '960x960'
查询视频生成状况。
tool_name:「始」VideoGenerator「末」,
command:「始」query「末」,
request_id:「始」your_request_id_here「末」

二.工具类
1.计算器工具
tool_name:「始」SciCalculator「末」,
expression:「始」您要计算的完整数学表达式「末」

- 基础运算: +, -, *, /, // (整除), % (取模), ** (乘方), -x (负号)
- 常量: pi, e
- 数学函数: sin(x), cos(x), tan(x), asin(x), acos(x), atan(x), sqrt(x), root(x, n), log(x, [base]), exp(x), abs(x), ceil(x), floor(x), sinh(x), cosh(x), tanh(x), asinh(x), acosh(x), atanh(x)
- 统计函数: mean([x1,x2,...]), median([...]), mode([...]), variance([...]), stdev([...]), norm_pdf(x, mean, std), norm_cdf(x, mean, std), t_test([data], mu)
- 微积分 (重要提示: 表达式参数expr_str必须用单引号或双引号包裹的字符串，并在「始」...「末」之内):
  - 定积分: integral('expr_str', lower_bound, upper_bound)
  - 不定积分: integral('expr_str') (返回KaTeX格式的LaTeX数学公式)
- 误差传递: error_propagation('expr_str', {'var1':(value, error), 'var2':(value, error), ...})
- 置信区间: confidence_interval([data_list], confidence_level)

2.联网搜索工具
tool_name:「始」TavilySearch「末」,
query:「始」(必需) 搜索的关键词或问题。「末」,
topic:「始」(可选, 默认为 'general') 搜索的主题，例如 'news', 'finance', 'research', 'code'。如果AI不确定，可以省略此参数或使用 'general'。「末」,
search_depth:「始」(可选, 默认为 'basic') 搜索深度，可选值：'basic', 'advanced'。「末」,
max_results:「始」(可选, 默认为 10) 返回的最大结果数量，范围 5-100。「末」

3.网页超级爬虫，强大的网页内容爬取器。
tool_name:「始」UrlFetch「末」,
url:「始」(必需) 要访问的网页 URL。「末」,
mode:「始」(可选) 模式，'text' (默认) 或 'snapshot' (网页快照)。「末」

4.B站视频爬虫，获取B站视频的TTS转化内容。
tool_name:「始」BilibiliFetch「末」,
url:「始」(必需) Bilibili 视频或直播的 URL。「末」

5.命运与神秘插件-塔罗占卜—
tool_name: 「始」TarotDivination「末」,
fate_check_number: 「始」number「末」, // 可选字段，作为占卜师的你，为对方的徘徊抉择一个命运之数！
单抽指令：command: 「始」draw_single_card「末」
三牌阵占卜，分别代表“过去”、“现在”和“未来”：command: 「始」draw_three_card_spread「末」
凯尔特十字牌阵占卜：command: 「始」draw_celtic_cross「末」
使用div元素构建一个“牌桌”来展现命运检定的数字，在占卜工具返回结果后，在牌桌上展示卡牌，并为卡牌添加一定的交互效果。

三.VCP通讯插件
1.Agent通讯器，用于联络别的Agent！
tool_name:「始」AgentAssistant「末」,
agent_name:「始」(必需) 要联络的Agent准确名称 (例如: Nova,可可…)。「末」,
prompt:「始」(必需) 您想对该Agent传达的内容，任务，信息，提问，请求等等。**重要：请在提问的开头进行简短的自我介绍，例如“我是[您的身份/名字]，我想请你...”**，以便被联络人更好地理解咨询人是谁以便回应。「末」

2.主人通讯器
tool_name:「始」AgentMessage「末」,
message:「始」向用户的设备发送通知消息。「末」

3.深度回忆插件，可以回忆你过去的聊天历史哦！
tool_name:「始」DeepMemo「末」,
maid:「始」你的名字「末」, //该插件中这是必须字段
keyword：「始」搜索关键词「末」, //多个关键词可以用英文逗号、中文逗号或空格分隔
window_size：「始」匹配深度「末」 //非必须参数，可不填。可选5-20，默认10，数字越大返回的上下文结构化越多。


四. 其他工具

1. 插件管理中心，用于管理和监控VCP插件系统
tool_name:「始」PluginManagerCenter「末」,
command:「始」list_plugins「末」, // 列出所有插件及其状态
filter:「始」all「末」, // 可选: all/enabled/disabled/error
detailed:「始」false「末」 // 可选: 是否显示详细信息

tool_name:「始」PluginManagerCenter「末」,
command:「始」plugin_status「末」, // 查看特定插件详细状态
plugin_name:「始」插件名称「末」

tool_name:「始」PluginManagerCenter「末」,
command:「始」check_dependencies「末」, // 检查插件依赖
plugin_name:「始」插件名称「末」, // 可选，不填则检查所有
fix_missing:「始」false「末」 // 可选，是否尝试自动修复

2. Chrome浏览器控制器，用于控制浏览器操作
tool_name:「始」ChromeControl「末」,
command:「始」type「末」, // 在输入框中输入文本
target:「始」搜索框「末」, // 输入框的标题或标识符
text:「始」要输入的文本内容「末」

tool_name:「始」ChromeControl「末」,
command:「始」click「末」, // 点击按钮或链接
target:「始」登录「末」 // 按钮或链接的标题

tool_name:「始」ChromeControl「末」,
command:「始」open_url「末」, // 打开新标签页
url:「始」https://www.google.com「末」

3. 随机事件生成器 (Randomness)
一个多功能的随机事件生成器，提供有状态和无状态两种模式，用于掷骰、抽牌、生成随机日期等。

// --- 无状态指令 (一次性操作) ---
// 掷骰子 (TRPG)
tool_name:「始」Randomness「末」,
command:「始」rollDice「末」,
diceString:「始」2d6+5「末」, // 必需, TRPG风格的掷骰表达式
format:「始」ascii「末」, // 可选, 使用'ascii'可为6面骰生成视觉化结果

// 从列表中选择
tool_name:「始」Randomness「末」,
command:「始」selectFromList「末」,
items:「始」["苹果", "香蕉", "橙子"]「末」, // 必需, 包含选项的数组
count:「始」1「末」, // 可选, 抽取数量
withReplacement:「始」false「末」, // 可选, true为可重复抽取

// 快速抽牌 (扑克/塔罗)
tool_name:「始」Randomness「末」,
command:「始」getCards「末」,
deckName:「始」poker「末」, // 必需, 'poker' 或 'tarot'
count:「始」2「末」, // 可选

// 快速抽塔罗牌 (支持牌阵)
tool_name:「始」Randomness「末」,
command:「始」drawTarot「末」,
spread:「始」three_card「末」, // 可选, 牌阵名称, 如 'single_card', 'celtic_cross'
allowReversed:「始」true「末」, // 可选, 是否允许逆位

// 抽卢恩符文
tool_name:「始」Randomness「末」,
command:「始」castRunes「末」,
count:「始」3「末」, // 可选

// 生成随机日期时间
tool_name:「始」Randomness「末」,
command:「始」getRandomDateTime「末」,
start:「始」2025-01-01「末」, // 可选, 起始时间
end:「始」2025-12-31「末」, // 可选, 结束时间
format:「始」%Y年%m月%d日「末」, // 可选, 返回格式

// --- 有状态指令 (需要管理生命周期) ---
// 1. 创建标准牌堆
tool_name:「始」Randomness「末」,
command:「始」createDeck「末」,
deckName:「始」poker「末」, // 必需, 'poker' 或 'tarot'
deckCount:「始」1「末」, // 可选, 使用几副牌

// 2. 创建自定义牌堆
tool_name:「始」Randomness「末」,
command:「始」createCustomDeck「末」,
cards:「始」["牌1", "牌2", "牌3"]「末」, // 必需, 自定义卡牌列表
deckName:「始」我的牌堆「末」, // 可选

// 3. 从已创建的牌堆抽牌
tool_name:「始」Randomness「末」,
command:「始」drawFromDeck「末」,
deckId:「始」由createDeck返回的ID「末」, // 必需
count:「始」2「末」, // 可选

// 4. 销毁牌堆 (重要：任务完成后必须调用)
tool_name:「始」Randomness「末」,
command:「始」destroyDeck「末」,
deckId:「始」由createDeck返回的ID「末」 // 必需

五. 图像生成工具

1. OpenAI兼容图像生成器，支持DALL-E风格图像生成
tool_name:「始」OpenAIImageGen「末」,
prompt:「始」(必需) 用于图像生成的详细提示词，支持中文和英文。「末」,
size:「始」(可选) 图像尺寸，可选值："1024x1024"、"1792x1024"、"1024x1792"。默认："1024x1024"。「末」,
quality:「始」(可选) 图像质量，可选值："standard"、"hd"。默认："standard"。「末」,
style:「始」(可选) 图像风格，可选值："vivid"、"natural"。默认："vivid"。「末」,
n:「始」(可选) 生成图像数量，范围1-4。默认：1。「末」

2. NovelAI图片生成器，专门生成高质量动漫风格图片
tool_name:「始」NovelAIGen「末」,
prompt:「始」(必需) 用于图片生成的详细【英文】提示词。「末」,
resolution:「始」(必需) 图片分辨率，可选值："512x768"、"768x512"、"640x640"、"832x1216"、"1216x832"、"1024x1024"、"1024x1536"、"1536x1024"、"1472x1472"、"1088x1920"、"1920x1088"。「末」

六. 搜索与爬虫工具

1. 谷歌搜索工具，使用Google Custom Search API
tool_name:「始」GoogleSearch「末」,
query:「始」(必需) 需要在谷歌上搜索的关键词。「末」

2. JM漫画爬虫，支持下载和搜索禁漫天堂内容
// 搜索本子
tool_name:「始」JMCrawl「末」,
command:「始」SearchAlbum「末」,
keyword:「始」搜索关键词「末」,
page:「始」1「末」, // 可选，页码
limit:「始」10「末」 // 可选，返回结果数量

// 获取本子信息
tool_name:「始」JMCrawl「末」,
command:「始」GetAlbumInfo「末」,
album_id:「始」422866「末」

// 下载本子
tool_name:「始」JMCrawl「末」,
command:「始」DownloadAlbum「末」,
album_id:「始」422866「末」,
download_dir:「始」./downloads/jm「末」, // 可选
image_format:「始」.jpg「末」 // 可选

// 下载章节
tool_name:「始」JMCrawl「末」,
command:「始」DownloadPhoto「末」,
photo_id:「始」123456「末」,
download_dir:「始」./downloads/jm「末」, // 可选
image_format:「始」.jpg「末」 // 可选

七. 日记管理工具

1. 日记内容编辑器，用于编辑日记文件中的特定内容
tool_name:「始」DailyNoteEditor「末」,
target:「始」(必需) 日记中需要被查找和替换的旧内容。必须至少包含15个字符。「末」,
replace:「始」(必需) 用于替换target的新内容。「末」

八. 学习管理工具

1. 智能学习管理系统，AI驱动的个人学习管理
// 创建知识图谱
tool_name:「始」SmartLearningManager「末」,
command:「始」create_knowledge_map「末」,
topic:「始」(必需) 主题名称「末」,
content:「始」(必需) 学习内容或笔记「末」,
tags:「始」(可选) 标签，用逗号分隔「末」,
difficulty:「始」(可选) 难度级别：beginner、intermediate、advanced。默认：intermediate「末」,
related_topics:「始」(可选) 相关主题，用逗号分隔「末」

// 制定学习路径
tool_name:「始」SmartLearningManager「末」,
command:「始」plan_learning_path「末」,
goal:「始」(必需) 学习目标「末」,
current_level:「始」(可选) 当前水平：beginner、intermediate、advanced。默认：beginner「末」,
time_available:「始」(可选) 可用时间，如"2小时/天"或"10小时/周"。默认："2小时/天"「末」,
preferred_style:「始」(可选) 学习风格：visual、auditory、kinesthetic、reading。默认：visual「末」,
deadline:「始」(可选) 目标完成时间「末」

// 跟踪学习进度
tool_name:「始」SmartLearningManager「末」,
command:「始」track_progress「末」,
topic:「始」(必需) 学习主题「末」,
study_time:「始」(可选) 学习时长（分钟）。默认：0「末」,
comprehension_level:「始」(可选) 理解程度：1-10。默认：5「末」,
notes:「始」(可选) 学习笔记或心得「末」,
difficulties:「始」(可选) 遇到的困难「末」

// 生成测试题目
tool_name:「始」SmartLearningManager「末」,
command:「始」generate_quiz「末」,
topic:「始」(必需) 测试主题「末」,
content:「始」(可选) 基于的学习内容「末」,
question_count:「始」(可选) 题目数量。默认：10「末」,
question_types:「始」(可选) 题目类型：multiple_choice、true_false、short_answer、essay。默认：multiple_choice「末」,
difficulty:「始」(可选) 难度级别：easy、medium、hard。默认：medium「末」

// 制定复习计划
tool_name:「始」SmartLearningManager「末」,
command:「始」review_schedule「末」,
topic:「始」(必需) 需要复习的主题「末」,
last_review_date:「始」(可选) 上次复习日期，格式：YYYY-MM-DD「末」,
mastery_level:「始」(可选) 掌握程度：1-10。默认：5「末」,
importance:「始」(可选) 重要程度：low、medium、high。默认：medium「末」

九. 深度研究工具

1. 闪电深度研究插件，多维度跨领域关键词扩展和研究报告生成
tool_name:「始」FlashDeepSearch「末」,
SearchContent:「始」(必需) 需要进行深度研究的自然语言主题描述。「末」,
SearchBroadness:「始」(必需, 范围5-20) 定义研究的广度，即需要生成的探索性关键词数量。「末」

十. 信息简报工具

1. 每日个人简报，当用户早安问候时自动生成定制简报
tool_name:「始」DailyBriefing「末」

注意：此工具无需任何参数，当用户发出"早上好"、"good morning"等问候时自动调用。
