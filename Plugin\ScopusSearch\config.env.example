# ===== Scopus API 配置 =====
SCOPUS_API_KEY=7d6ed71c1e26d2613604e4e4078d0faf

# ===== 静态推送功能配置 =====
# 注意：以下配置仅用于静态推送功能（系统占位符 {{ScopusDailyPapersData}}）
# AGENT工具调用时会使用用户实时输入的搜索词，不依赖这些配置

# 静态推送的默认搜索关键词（每30分钟自动执行）
SCOPUS_SEARCH_TERMS="artificial intelligence" OR "machine learning" OR "deep learning"

# 静态推送的最大结果数量
SCOPUS_MAX_RESULTS=50

# 静态推送的天数范围（获取最近N天的论文）
SCOPUS_DAYS_RANGE=7

# ===== PDF下载配置 =====
# 是否启用PDF下载功能（适用于静态推送和AGENT调用）
PDF_DOWNLOAD_ENABLED=true

# PDF文件保存路径
PDF_DOWNLOAD_PATH=./scopus_papers

# 是否启用第三方PDF下载服务（如Sci-Hub等）
THIRD_PARTY_ENABLED=false

# Sci-Hub镜像地址
SCIHUB_MIRROR=https://sci-hub.se

# ===== 调试配置 =====
# 启用调试模式以获得详细日志输出
SCOPUS_DEBUG_MODE=false